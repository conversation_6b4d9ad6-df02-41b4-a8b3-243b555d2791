# Etapa 1 Validation for "Carga" Subprocess

## Overview

This document describes the implementation of Etapa 1 validation for the "carga" subprocess according to the 20241010_10766_Instructivo_Oferta_Academica_2025_UNIV_FFAA requirements.

## Implementation Details

### New Function: `validateEtapa1Carga`

A specialized validation function has been implemented specifically for Etapa 1 of the "carga" subprocess. This function provides comprehensive validation rules that are more strict and specific than the general validation.

**Location**: `src/controllers/Oferta-Academica-David/oaOfertaAcademicaController.js`

### Key Validation Rules

#### 1. Required Fields
All the following fields are mandatory for Etapa 1 - Carga:
- `cod_sede`, `nombre_sede`
- `cod_carrera` (specifically required for etapa 1)
- `nombre_carrera`
- `modalidad`, `cod_jornada`, `version`
- `cod_tipo_plan_carrera`, `caracteristicas_tipo_plan`
- `duracion_estudios`, `duracion_titulacion`, `duracion_total`
- `regimen`, `duracion_regimen`
- `nombre_titulo`, `nombre_grado`
- `cod_nivel_global`, `cod_nivel_carrera`, `cod_demre`
- `anio_inicio`, `acreditacion`
- `elegible_beca_pedagogia`, `ped_med_odont_otro`
- `requisito_ingreso`, `semestres_reconocidos`
- `area_actual`
- All ponderacion fields (nem, ranking, c_lectora, matematicas, matematicas_2, historia, ciencias, otros)
- `vacantes_primer_semestre`, `vacantes_segundo_semestre`, `vacantes_pace`
- `vigencia_carrera`

#### 2. Format Validations
- **Uppercase Fields**: `nombre_sede`, `nombre_carrera`, `caracteristicas_tipo_plan`, `nombre_titulo`, `nombre_grado` must be in uppercase and contain only letters, numbers, and spaces.
- **Numeric Fields**: All numeric fields must contain valid numbers.

#### 3. Value Range Validations
- `modalidad`: 1 (Presencial), 2 (Semipresencial), 3 (No Presencial)
- `cod_jornada`: 1 (Diurna), 2 (Vespertina), 3 (Semi Presencial), 4 (A Distancia), 5 (Otra)
- `vigencia_carrera`: 1 (Vigente), 2 (No Vigente), 3 (Suspendida)
- `acreditacion`: 1 (Sí), 2 (No)
- `elegible_beca_pedagogia`: 0 (No elegible), 1 (Programa de Pedagogía Elegible), 2 (Programa de Licenciatura Elegible), 3 (Programa de Formación Pedagógica Elegible)
- `ped_med_odont_otro`: 'P' (Pedagogía), 'M' (Medicina), 'D' (Odontología), 'O' (Otro)
- `requisito_ingreso`: 1-10 (various education levels)
- `area_destino_*` fields: 0 (no beneficio de continuidad), 1 (sí existe beneficio de continuidad)

#### 4. Conditional Validations
For active programs (`vigencia_carrera = 1`):
- `malla_curricular` is required
- `perfil_egreso` is required  
- `mail_difusion_carrera` is required

#### 5. Ponderaciones Validation
- All ponderacion fields must be between 0-100%
- The sum of all ponderaciones must equal exactly 100%

## API Endpoints

### 1. Validation Endpoint
**POST** `/oa_oferta_academica/validate_etapa1_carga`

Validates CSV data specifically for Etapa 1 - Carga without importing it.

**Request Body:**
```json
{
  "etapa_id": "number",
  "csvData": [
    {
      "COD_SEDE": "value",
      "NOMBRE_SEDE": "value",
      "COD_CARRERA": "value",
      // ... other fields
    }
  ]
}
```

**Response (Success):**
```json
{
  "message": "Validación exitosa para Etapa 1 - Carga",
  "totalRows": 100,
  "validationPassed": true
}
```

**Response (Validation Errors):**
```json
{
  "message": "Errores de validación encontrados para Etapa 1 - Carga",
  "errors": "Fila 1: cod_carrera es obligatorio para la etapa 1; Fila 2: modalidad debe ser 1, 2, o 3",
  "totalErrors": 2,
  "totalRows": 100
}
```

### 2. Import with Validation
The existing import endpoints now automatically apply Etapa 1 validation when importing data for Etapa 1 - Carga:
- **POST** `/oa_oferta_academica/import_csv/:etapa_id`
- **POST** `/oa_oferta_academica/import_csv_total`

## Integration

The Etapa 1 validation is automatically integrated into:
1. `importOfertasAcademicasFromCSV` - Single etapa import
2. `importOfertasAcademicasFromCSVTotal` - Multi-etapa import
3. New dedicated validation endpoint `validateEtapa1CargaData`

## Usage Examples

### Frontend Integration
```javascript
// Validate data before import
const validateData = async (etapaId, csvData) => {
  try {
    const response = await fetch('/api/oa_oferta_academica/validate_etapa1_carga', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({
        etapa_id: etapaId,
        csvData: csvData
      })
    });
    
    const result = await response.json();
    
    if (response.ok) {
      console.log('Validation passed:', result.message);
      return { valid: true, errors: null };
    } else {
      console.log('Validation errors:', result.errors);
      return { valid: false, errors: result.errors };
    }
  } catch (error) {
    console.error('Validation request failed:', error);
    return { valid: false, errors: 'Network error' };
  }
};
```

## Error Messages

All error messages are in Spanish and follow the pattern:
- `"Etapa 1 - [description of error]"`
- Row numbers are included for easy identification
- Multiple errors per row are concatenated with semicolons

## Testing

To test the validation:
1. Create test data with various validation scenarios
2. Call the validation endpoint
3. Verify that appropriate errors are returned
4. Test with valid data to ensure it passes

## Notes

- The validation is only applied when `etapa.etapa === 1` and `etapa.tipo === "carga"`
- Column names are automatically normalized to lowercase for processing
- Text fields are automatically converted to uppercase where required
- Empty strings and null values are properly handled
- The validation complements the existing general validation, providing additional specific rules for Etapa 1
