/**
 * Test script for Etapa 1 Carga validation
 * This script demonstrates how to test the new validation functionality
 */

// Sample test data for Etapa 1 - Carga validation
const validTestData = [
  {
    COD_SEDE: "1",
    NOMBRE_SEDE: "SEDE PRINCIPAL",
    COD_CARRERA: "12345",
    NOMBRE_CARRERA: "INGENIERIA CIVIL",
    MODALIDAD: "1",
    COD_JORNADA: "1",
    VERSION: "1",
    COD_TIPO_PLAN_CARRERA: "1",
    CARACTERISTICAS_TIPO_PLAN: "PLAN REGULAR",
    DURACION_ESTUDIOS: "10",
    DURACION_TITULACION: "1",
    DURACION_TOTAL: "11",
    <PERSON><PERSON><PERSON><PERSON>: "1",
    DURACION_REGIMEN: "2",
    NOMBRE_TITULO: "INGENIERO CIVIL",
    NOMBRE_GRADO: "LICENCIADO EN CIENCIAS DE LA INGENIERIA",
    COD_NIVEL_GLOBAL: "1",
    COD_<PERSON>IVEL_CARRERA: "4",
    COD_DEMRE: "1234",
    ANIO_INICIO: "2025",
    ACREDITACION: "1",
    ELEGIBLE_BECA_PEDAGOGIA: "0",
    PED_MED_ODONT_OTRO: "O",
    REQUISITO_INGRESO: "1",
    SEMESTRES_RECONOCIDOS: "0",
    AREA_ACTUAL: "6",
    AREA_DESTINO_AGRICULTURA: "0",
    AREA_DESTINO_CIENCIAS: "0",
    AREA_DESTINO_CS_SOCIALES: "0",
    AREA_DESTINO_EDUCACION: "0",
    AREA_DESTINO_HUMANIDADES: "0",
    AREA_DESTINO_INGENIERIA: "1",
    AREA_DESTINO_SALUD: "0",
    AREA_DESTINO_SERVICIOS: "0",
    PONDERACION_NEM: "10",
    PONDERACION_RANKING: "20",
    PONDERACION_C_LECTORA: "20",
    PONDERACION_MATEMATICAS: "30",
    PONDERACION_MATEMATICAS_2: "20",
    PONDERACION_HISTORIA: "0",
    PONDERACION_CIENCIAS: "0",
    PONDERACION_OTROS: "0",
    VACANTES_PRIMER_SEMESTRE: "50",
    VACANTES_SEGUNDO_SEMESTRE: "0",
    VACANTES_PACE: "5",
    VIGENCIA_CARRERA: "1",
    MALLA_CURRICULAR: "https://example.com/malla",
    PERFIL_EGRESO: "Perfil de egreso del ingeniero civil",
    MAIL_DIFUSION_CARRERA: "<EMAIL>"
  }
];

const invalidTestData = [
  {
    // Missing required fields
    COD_SEDE: "1",
    NOMBRE_SEDE: "sede principal", // Should be uppercase
    // COD_CARRERA missing - required for etapa 1
    NOMBRE_CARRERA: "ingenieria civil", // Should be uppercase
    MODALIDAD: "4", // Invalid value (should be 1, 2, or 3)
    COD_JORNADA: "1",
    VERSION: "1",
    COD_TIPO_PLAN_CARRERA: "1",
    CARACTERISTICAS_TIPO_PLAN: "plan regular", // Should be uppercase
    DURACION_ESTUDIOS: "10",
    DURACION_TITULACION: "1",
    DURACION_TOTAL: "11",
    REGIMEN: "1",
    DURACION_REGIMEN: "2",
    NOMBRE_TITULO: "ingeniero civil", // Should be uppercase
    NOMBRE_GRADO: "licenciado en ciencias", // Should be uppercase
    COD_NIVEL_GLOBAL: "1",
    COD_NIVEL_CARRERA: "4",
    COD_DEMRE: "1234",
    ANIO_INICIO: "2025",
    ACREDITACION: "3", // Invalid value (should be 1 or 2)
    ELEGIBLE_BECA_PEDAGOGIA: "5", // Invalid value (should be 0, 1, 2, or 3)
    PED_MED_ODONT_OTRO: "X", // Invalid value (should be P, M, D, or O)
    REQUISITO_INGRESO: "15", // Invalid value (should be 1-10)
    SEMESTRES_RECONOCIDOS: "0",
    AREA_ACTUAL: "6",
    AREA_DESTINO_AGRICULTURA: "2", // Invalid value (should be 0 or 1)
    AREA_DESTINO_CIENCIAS: "0",
    AREA_DESTINO_CS_SOCIALES: "0",
    AREA_DESTINO_EDUCACION: "0",
    AREA_DESTINO_HUMANIDADES: "0",
    AREA_DESTINO_INGENIERIA: "1",
    AREA_DESTINO_SALUD: "0",
    AREA_DESTINO_SERVICIOS: "0",
    PONDERACION_NEM: "10",
    PONDERACION_RANKING: "20",
    PONDERACION_C_LECTORA: "20",
    PONDERACION_MATEMATICAS: "30",
    PONDERACION_MATEMATICAS_2: "10", // Sum = 90%, should be 100%
    PONDERACION_HISTORIA: "0",
    PONDERACION_CIENCIAS: "0",
    PONDERACION_OTROS: "0",
    VACANTES_PRIMER_SEMESTRE: "50",
    VACANTES_SEGUNDO_SEMESTRE: "0",
    VACANTES_PACE: "5",
    VIGENCIA_CARRERA: "1",
    // Missing required fields for active programs (vigencia_carrera = 1):
    // MALLA_CURRICULAR, PERFIL_EGRESO, MAIL_DIFUSION_CARRERA
  }
];

/**
 * Test function to validate data using the API endpoint
 * @param {number} etapaId - The etapa ID
 * @param {Array} csvData - The CSV data to validate
 * @param {string} token - Authorization token
 */
async function testValidation(etapaId, csvData, token) {
  try {
    const response = await fetch('http://localhost:3000/api/oa_oferta_academica/validate_etapa1_carga', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({
        etapa_id: etapaId,
        csvData: csvData
      })
    });

    const result = await response.json();
    
    console.log('Response Status:', response.status);
    console.log('Response Body:', JSON.stringify(result, null, 2));
    
    return result;
  } catch (error) {
    console.error('Test failed:', error);
    return null;
  }
}

/**
 * Run validation tests
 */
async function runTests() {
  console.log('=== Etapa 1 Carga Validation Tests ===\n');
  
  // You need to replace these with actual values from your system
  const ETAPA_ID = 1; // Replace with actual etapa ID for etapa 1 carga
  const AUTH_TOKEN = 'your-auth-token-here'; // Replace with actual token
  
  console.log('Test 1: Valid data');
  console.log('Expected: Validation should pass');
  const validResult = await testValidation(ETAPA_ID, validTestData, AUTH_TOKEN);
  console.log('Result:', validResult?.validationPassed ? 'PASSED' : 'FAILED');
  console.log('\n' + '='.repeat(50) + '\n');
  
  console.log('Test 2: Invalid data');
  console.log('Expected: Validation should fail with multiple errors');
  const invalidResult = await testValidation(ETAPA_ID, invalidTestData, AUTH_TOKEN);
  console.log('Result:', invalidResult?.validationPassed === false ? 'PASSED (correctly failed)' : 'FAILED');
  if (invalidResult?.errors) {
    console.log('Errors found:', invalidResult.errors);
  }
  console.log('\n' + '='.repeat(50) + '\n');
  
  console.log('Tests completed!');
}

// Export for use in other test files
module.exports = {
  validTestData,
  invalidTestData,
  testValidation,
  runTests
};

// Run tests if this file is executed directly
if (require.main === module) {
  console.log('To run these tests, you need to:');
  console.log('1. Start your server');
  console.log('2. Update ETAPA_ID and AUTH_TOKEN in the runTests function');
  console.log('3. Uncomment the line below and run: node test/etapa1_carga_validation_test.js');
  console.log('');
  // runTests();
}
